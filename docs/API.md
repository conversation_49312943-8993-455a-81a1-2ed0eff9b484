# API Endpoints

This document provides a detailed overview of the available API endpoints, including their purpose, required parameters, and expected responses.

## User Management

### `POST /api/v1/users`

- **Description**: Registers a new user.
- **Request Body**: `CreateUserDto`
- **Response**:
  - `201`: User created successfully.
  - `400`: Invalid request body.
  - `409`: User already exists.

### `GET /api/v1/users/profile`

- **Description**: Retrieves the current user's profile.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User profile retrieved successfully.
  - `401`: Unauthorized.

### `GET /api/v1/users/search`

- **Description**: Searches for users.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Users found.
  - `401`: Unauthorized.

### `GET /api/v1/users/:id`

- **Description**: Retrieves a user by ID.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.
  - `404`: User not found.

### `PUT /api/v1/users/:id`

- **Description**: Updates a user.
- **Request Body**: `UpdateUserDto`
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `DELETE /api/v1/users/:id`

- **Description**: Deletes a user.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User deleted successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users`

- **Description**: Retrieves all users with filters and pagination (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/role/:role`

- **Description**: Retrieves users by role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/admin/stats`

- **Description**: Retrieves user statistics (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: User statistics retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/users/:id/role`

- **Description**: Updates a user's role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.UPDATE)`
- **Response**:
  - `200`: User role updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/users/email/:email`

- **Description**: Retrieves a user by email (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: User retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

## Authentication

### `POST /api/v1/auth/register`

- **Description**: Registers a new user.
- **Request Body**: `RegisterDto`
- **Middleware**: `detectSuspiciousActivity`, `registerRateLimit`, `validatePasswordStrength`
- **Response**:
  - `201`: User created successfully.
  - `400`: Invalid request body.
  - `429`: Too many requests.

### `POST /api/v1/auth/login`

- **Description**: Logs in a user.
- **Request Body**: `LoginDto`
- **Middleware**: `detectSuspiciousActivity`, `loginRateLimit`
- **Response**:
  - `200`: User logged in successfully.
  - `401`: Unauthorized.
  - `429`: Too many requests.

### `POST /api/v1/auth/refresh`

- **Description**: Refreshes a user's JWT token.
- **Middleware**: `rateLimiterMiddleware`
- **Response**:
  - `200`: Token refreshed successfully.
  - `401`: Unauthorized.

### `POST /api/v1/auth/logout`

- **Description**: Logs out a user.
- **Response**:
  - `200`: User logged out successfully.

### `POST /api/v1/auth/verify-email`

- **Description**: Verifies a user's email address.
- **Request Body**: `VerifyEmailDto`
- **Middleware**: `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Email verified successfully.
  - `400`: Invalid or expired token.
  - `429`: Too many requests.

### `POST /api/v1/auth/resend-verification`

- **Description**: Resends the email verification link.
- **Request Body**: `ResendVerificationDto`
- **Middleware**: `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Verification email sent successfully.
  - `400`: Bad request.
  - `429`: Too many requests.

### `GET /api/v1/auth/profile`

- **Description**: Retrieves the current user's profile.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: User profile retrieved successfully.
  - `401`: Unauthorized.

### `PATCH /api/v1/auth/profile`

- **Description**: Updates the current user's profile.
- **Request Body**: `UpdateUserDto`
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.PROFILE, Action.UPDATE)`
- **Response**:
  - `200`: User profile updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/auth/change-password`

- **Description**: Changes the current user's password.
- **Request Body**: `ChangePasswordDto`
- **Middleware**: `authMiddleware`, `detectSuspiciousActivity`, `passwordChangeRateLimit`, `validatePasswordStrength`
- **Response**:
  - `200`: Password changed successfully.
  - `401`: Unauthorized.
  - `429`: Too many requests.

### `POST /api/v1/auth/change-email`

- **Description**: Initiates an email address change.
- **Request Body**: `ChangeEmailDto`
- **Middleware**: `authMiddleware`, `detectSuspiciousActivity`, `emailVerificationRateLimit`
- **Response**:
  - `200`: Email change verification sent successfully.
  - `401`: Unauthorized.
  - `409`: Conflict.
  - `429`: Too many requests.

### `GET /api/v1/auth/permissions`

- **Description**: Retrieves the current user's permissions.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Permissions retrieved successfully.
  - `401`: Unauthorized.

### `GET /api/v1/auth/check-permission/:resource/:action`

- **Description**: Checks if the current user has a specific permission.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: Permission check successful.
  - `401`: Unauthorized.

### `GET /api/v1/auth/users`

- **Description**: Retrieves all users (admin only).
- **Middleware**: `authMiddleware`, `authorizationMiddleware([Role.ADMIN])`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/auth/users-rbac`

- **Description**: Retrieves all users (permission-based).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.READ)`
- **Response**:
  - `200`: Users retrieved successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `PATCH /api/v1/auth/users/:userId/role`

- **Description**: Updates a user's role (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.UPDATE)`
- **Response**:
  - `200`: User role updated successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `DELETE /api/v1/auth/users/:userId`

- **Description**: Deletes a user (admin only).
- **Middleware**: `authMiddleware`, `permissionMiddleware(Resource.USER, Action.DELETE)`
- **Response**:
  - `200`: User deleted successfully.
  - `401`: Unauthorized.
  - `403`: Forbidden.

### `GET /api/v1/auth/can-access-users`

- **Description**: Checks if the current user can access user management.
- **Middleware**: `authMiddleware`, `resourceAccessMiddleware(Resource.USER)`
- **Response**:
  - `200`: Access check successful.
  - `401`: Unauthorized.

### `GET /api/v1/auth/can-access-analytics`

- **Description**: Checks if the current user can access analytics.
- **Middleware**: `authMiddleware`, `resourceAccessMiddleware(Resource.ANALYTICS)`
- **Response**:
  - `200`: Access check successful.
  - `401`: Unauthorized.

## OAuth Authentication

### `GET /api/v1/auth/oauth/:provider`

- **Description**: Initiates OAuth authentication with the specified provider.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `validateProviderParam`, `validateProvider`, `oauthInitiateRateLimitMiddleware`, `generateOAuthState`
- **Response**:
  - `302`: Redirect to OAuth provider
  - `400`: Invalid provider or configuration error
  - `429`: Rate limit exceeded

### `GET /api/v1/auth/oauth/:provider/callback`

- **Description**: Handles OAuth callback from the provider.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Query Parameters**:
  - `code`: Authorization code from OAuth provider
  - `state`: State parameter for CSRF protection
  - `error`: Error code from OAuth provider (optional)
- **Middleware**: `validateProviderParam`, `oauthCallbackRateLimitMiddleware`
- **Response**:
  - `302`: Redirect to success or failure URL
  - `400`: Invalid callback parameters
  - `429`: Rate limit exceeded

### `GET /api/v1/auth/oauth/accounts`

- **Description**: Retrieves user's linked OAuth accounts.
- **Middleware**: `authMiddleware`
- **Response**:
  - `200`: List of linked OAuth accounts
  - `401`: Unauthorized

### `POST /api/v1/auth/oauth/link/:provider`

- **Description**: Links an OAuth account to the current user.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `authMiddleware`, `validateProviderParam`, `validateProvider`, `oauthLinkingRateLimitMiddleware`, `generateOAuthState`
- **Response**:
  - `302`: Redirect to OAuth provider for linking
  - `400`: Invalid provider or account already linked
  - `401`: Unauthorized
  - `429`: Rate limit exceeded

### `DELETE /api/v1/auth/oauth/unlink/:provider`

- **Description**: Unlinks an OAuth account from the current user.
- **Parameters**:
  - `provider`: OAuth provider (google, facebook, github)
- **Middleware**: `authMiddleware`, `validateProviderParam`, `validateProvider`, `oauthLinkingRateLimitMiddleware`
- **Response**:
  - `200`: OAuth account unlinked successfully
  - `400`: Cannot unlink the only authentication method
  - `401`: Unauthorized
  - `404`: OAuth account not found
