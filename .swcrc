{"jsc": {"parser": {"syntax": "typescript", "tsx": false, "decorators": true}, "transform": {"legacyDecorator": true, "decoratorMetadata": true}, "target": "es2020", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/controllers/*": ["src/controllers/*"], "@/middlewares/*": ["src/middlewares/*"], "@/models/*": ["src/models/*"], "@/routes/*": ["src/routes/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}}, "module": {"type": "commonjs"}, "sourceMaps": true}