{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "resolveJsonModule": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "moduleDetection": "force", "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": false, "noPropertyAccessFromIndexSignature": true, "noUnusedLocals": true, "noUnusedParameters": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/config/*": ["src/config/*"], "@/controllers/*": ["src/controllers/*"], "@/middlewares/*": ["src/middlewares/*"], "@/models/*": ["src/models/*"], "@/routes/*": ["src/routes/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}}, "include": ["src/**/*", "src/types/**/*.d.ts"], "exclude": ["node_modules", "dist"]}