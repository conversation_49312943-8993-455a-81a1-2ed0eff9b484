import {
  OAUTH_GOOGLE_CLIENT_ID,
  OAUTH_GOOGLE_CLIENT_SECRET,
  OAUTH_FACEBOOK_CLIENT_ID,
  OAUTH_FACEBOOK_CLIENT_SECRET,
  OAUTH_GITHUB_CLIENT_ID,
  OAUTH_GITHUB_CLIENT_SECRET,
  OAUTH_REDIRECT_BASE_URL,
  OAUTH_SUCCESS_REDIRECT,
  OAUTH_FAILURE_REDIRECT,
} from './index';

/**
 * OAuth Provider Types
 */
export type OAuthProvider = 'google' | 'facebook' | 'github';

/**
 * OAuth Provider Configuration Interface
 */
export interface OAuthProviderConfig {
  clientID: string;
  clientSecret: string;
  callbackURL: string;
  scope: string[];
  profileFields?: string[];
}

/**
 * OAuth Configuration Class
 * Centralizes OAuth provider configurations with security best practices
 */
export class OAuthConfig {
  /**
   * Get Google OAuth configuration
   */
  static getGoogleConfig(): OAuthProviderConfig {
    return {
      clientID: OAUTH_GOOGLE_CLIENT_ID,
      clientSecret: OAUTH_GOOGLE_CLIENT_SECRET,
      callbackURL: `${OAUTH_REDIRECT_BASE_URL}/api/v1/auth/oauth/google/callback`,
      scope: ['profile', 'email'],
    };
  }

  /**
   * Get Facebook OAuth configuration
   */
  static getFacebookConfig(): OAuthProviderConfig {
    return {
      clientID: OAUTH_FACEBOOK_CLIENT_ID,
      clientSecret: OAUTH_FACEBOOK_CLIENT_SECRET,
      callbackURL: `${OAUTH_REDIRECT_BASE_URL}/api/v1/auth/oauth/facebook/callback`,
      scope: ['email', 'public_profile'],
      profileFields: ['id', 'emails', 'name', 'picture.type(large)'],
    };
  }

  /**
   * Get GitHub OAuth configuration
   */
  static getGitHubConfig(): OAuthProviderConfig {
    return {
      clientID: OAUTH_GITHUB_CLIENT_ID,
      clientSecret: OAUTH_GITHUB_CLIENT_SECRET,
      callbackURL: `${OAUTH_REDIRECT_BASE_URL}/api/v1/auth/oauth/github/callback`,
      scope: ['user:email'],
    };
  }

  /**
   * Get all provider configurations
   */
  static getAllConfigs(): Record<OAuthProvider, OAuthProviderConfig> {
    return {
      google: this.getGoogleConfig(),
      facebook: this.getFacebookConfig(),
      github: this.getGitHubConfig(),
    };
  }

  /**
   * Check if a provider is enabled (has valid configuration)
   */
  static isProviderEnabled(provider: OAuthProvider): boolean {
    const config = this.getProviderConfig(provider);
    return !!(config.clientID && config.clientSecret);
  }

  /**
   * Get configuration for a specific provider
   */
  static getProviderConfig(provider: OAuthProvider): OAuthProviderConfig {
    switch (provider) {
      case 'google':
        return this.getGoogleConfig();
      case 'facebook':
        return this.getFacebookConfig();
      case 'github':
        return this.getGitHubConfig();
      default:
        throw new Error(`Unsupported OAuth provider: ${provider}`);
    }
  }

  /**
   * Get enabled providers
   */
  static getEnabledProviders(): OAuthProvider[] {
    const providers: OAuthProvider[] = ['google', 'facebook', 'github'];
    return providers.filter(provider => this.isProviderEnabled(provider));
  }

  /**
   * Get redirect URLs
   */
  static getRedirectUrls() {
    return {
      success: OAUTH_SUCCESS_REDIRECT,
      failure: OAUTH_FAILURE_REDIRECT,
      baseUrl: OAUTH_REDIRECT_BASE_URL,
    };
  }

  /**
   * Validate OAuth configuration on startup
   */
  static validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const enabledProviders = this.getEnabledProviders();

    if (enabledProviders.length === 0) {
      errors.push('No OAuth providers are configured. At least one provider should be enabled.');
    }

    if (!OAUTH_REDIRECT_BASE_URL) {
      errors.push('OAUTH_REDIRECT_BASE_URL is required for OAuth functionality.');
    }

    if (!OAUTH_SUCCESS_REDIRECT) {
      errors.push('OAUTH_SUCCESS_REDIRECT is required for OAuth functionality.');
    }

    if (!OAUTH_FAILURE_REDIRECT) {
      errors.push('OAUTH_FAILURE_REDIRECT is required for OAuth functionality.');
    }

    // Validate each enabled provider
    enabledProviders.forEach(provider => {
      const config = this.getProviderConfig(provider);
      if (!config.clientID) {
        errors.push(`${provider.toUpperCase()} OAuth client ID is missing.`);
      }
      if (!config.clientSecret) {
        errors.push(`${provider.toUpperCase()} OAuth client secret is missing.`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * OAuth State Management
 * Handles secure state parameter generation and validation for CSRF protection
 */
export class OAuthStateManager {
  private static readonly STATE_TTL = 10 * 60; // 10 minutes in seconds


  /**
   * Generate secure state parameter
   */
  static generateState(userId?: string): string {
    const timestamp = Date.now().toString();
    const randomBytes = Math.random().toString(36).substring(2, 15);
    const userPart = userId ? userId.substring(0, 8) : 'anon';
    return `${timestamp}_${randomBytes}_${userPart}`;
  }

  /**
   * Validate state parameter
   */
  static validateState(state: string): boolean {
    if (!state || typeof state !== 'string') {
      return false;
    }

    const parts = state.split('_');
    if (parts.length !== 3) {
      return false;
    }

    const timestamp = parseInt(parts[0] || '0', 10);
    if (isNaN(timestamp)) {
      return false;
    }

    // Check if state is not expired (10 minutes)
    const now = Date.now();
    const stateAge = now - timestamp;
    const maxAge = this.STATE_TTL * 1000; // Convert to milliseconds

    return stateAge <= maxAge;
  }

  /**
   * Extract user ID from state (if available)
   */
  static extractUserIdFromState(state: string): string | null {
    if (!this.validateState(state)) {
      return null;
    }

    const parts = state.split('_');
    const userPart = parts[2];
    return userPart === 'anon' ? null : userPart || null;
  }
}

export default OAuthConfig;
