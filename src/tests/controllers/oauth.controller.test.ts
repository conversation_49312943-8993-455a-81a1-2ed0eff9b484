import request from 'supertest';
import express from 'express';
import { OAuthController } from '../../controllers/oauth.controller';
import { OAuthService } from '../../services/oauth.service';
import { OAuthConfig } from '../../config/oauth.config';
import { PassportConfig } from '../../config/passport.config';
import { HttpException } from '../../exceptions/HttpException';

// Mock dependencies
jest.mock('../../services/oauth.service');
jest.mock('../../config/oauth.config');
jest.mock('../../config/passport.config');
jest.mock('../../utils/secureLogger');
jest.mock('../../utils/cookieUtils');
jest.mock('../../services/sessionVersioning.service');

const mockOAuthService = OAuthService as jest.Mocked<typeof OAuthService>;
const mockOAuthConfig = OAuthConfig as jest.Mocked<any>;
const mockPassportConfig = PassportConfig as jest.Mocked<any>;

// Create test app
const app = express();
app.use(express.json());
app.use(express.json());

// Mock session middleware with fixed session state
app.use((req, _res, next) => {
  req.session = {
    oauthState: 'state_123', // Fixed state for most tests
    oauthStateTimestamp: Date.now(),
  } as any;
  next();
});

// Mock auth middleware
const mockAuthMiddleware = (req: any, _res: any, next: any) => {
  req.user = { id: 'user123', email: '<EMAIL>' };
  next();
};

// Setup routes
app.get('/oauth/providers', OAuthController.getAvailableProviders);
app.get('/oauth/:provider', OAuthController.initiateAuth);
app.get('/oauth/:provider/callback', OAuthController.handleCallback);
app.get('/oauth/accounts', mockAuthMiddleware, OAuthController.getLinkedAccounts);
app.post('/oauth/link/:provider', mockAuthMiddleware, OAuthController.linkAccount);
app.delete('/oauth/unlink/:provider', mockAuthMiddleware, OAuthController.unlinkAccount);

// Error handling middleware (must be last)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use((error: any, _req: any, res: any, _next: any) => {
  if (error instanceof HttpException) {
    res.status(error.status).json({ message: error.message });
  } else {
    res.status(500).json({ message: 'Something went wrong' });
  }
});

describe('OAuthController', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Setup OAuth config mocks
    (mockOAuthConfig.isProviderEnabled as jest.Mock).mockReturnValue(true);
    (mockOAuthConfig.getEnabledProviders as jest.Mock).mockReturnValue(['google', 'facebook']);
    (mockOAuthConfig.getRedirectUrls as jest.Mock).mockReturnValue({
      success: 'http://localhost:3000/dashboard',
      failure: 'http://localhost:3000/login?error=oauth_failed',
    });
  });

  describe('GET /oauth/providers', () => {
    it('should return available OAuth providers', async () => {
      (mockOAuthConfig.getEnabledProviders as jest.Mock).mockReturnValue(['google', 'facebook']);

      const response = await request(app).get('/oauth/providers');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: {
          providers: [
            {
              name: 'google',
              displayName: 'Google',
              authUrl: '/api/v1/auth/oauth/google',
              enabled: true,
            },
            {
              name: 'facebook',
              displayName: 'Facebook',
              authUrl: '/api/v1/auth/oauth/facebook',
              enabled: true,
            },
          ],
          total: 2,
        },
        timestamp: expect.any(String),
      });
    });

    it('should handle errors when getting providers', async () => {
      (mockOAuthConfig.getEnabledProviders as jest.Mock).mockImplementation(() => {
        throw new Error('Configuration error');
      });

      const response = await request(app).get('/oauth/providers');

      expect(response.status).toBe(500);
    });
  });

  describe('GET /oauth/:provider', () => {
    it('should initiate OAuth authentication for valid provider', async () => {
      (mockOAuthConfig.isProviderEnabled as jest.Mock).mockReturnValue(true);
      (mockOAuthConfig.getProviderConfig as jest.Mock).mockReturnValue({
        clientID: 'test_client_id',
        clientSecret: 'test_client_secret',
        callbackURL: 'http://localhost:3000/auth/oauth/google/callback',
        scope: ['profile', 'email'],
      });

      // Mock Passport middleware
      const mockPassportMiddleware = jest.fn((_req, res) => {
        res.redirect('https://accounts.google.com/oauth/authorize?client_id=test');
      });
      (mockPassportConfig.getAuthenticateMiddleware as jest.Mock).mockReturnValue(mockPassportMiddleware);

      const response = await request(app).get('/oauth/google');

      expect(response.status).toBe(302);
      expect(mockOAuthConfig.isProviderEnabled).toHaveBeenCalledWith('google');
      expect(mockPassportConfig.getAuthenticateMiddleware).toHaveBeenCalledWith('google', expect.any(Object));
    });

    it('should reject disabled OAuth provider', async () => {
      (mockOAuthConfig.isProviderEnabled as jest.Mock).mockReturnValue(false);

      const response = await request(app).get('/oauth/disabled-provider');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('not enabled or configured');
    });

    it('should handle invalid provider', async () => {
      (mockOAuthConfig.isProviderEnabled as jest.Mock).mockReturnValue(false);

      const response = await request(app).get('/oauth/invalid');

      expect(response.status).toBe(400);
    });
  });

  describe('GET /oauth/:provider/callback', () => {
    it('should handle successful OAuth callback', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthConfig.getRedirectUrls.mockReturnValue({
        success: 'http://localhost:3000/dashboard',
        failure: 'http://localhost:3000/login?error=oauth_failed',
        baseUrl: 'http://localhost:3000',
      });

      // Mock successful OAuth result
      const mockOAuthResult = {
        user: { id: 'user123', email: '<EMAIL>' },
        token: 'jwt_token',
        refreshToken: 'refresh_token',
        expiresIn: '7d',
        tokenType: 'Bearer',
        isNewUser: false,
      };

      mockOAuthService.handleOAuthCallback.mockResolvedValue(mockOAuthResult);
      mockOAuthService.findUserById.mockResolvedValue({
        id: 'user123',
        email: '<EMAIL>',
      } as any);

      // Mock Passport middleware for successful authentication
      const mockPassportMiddleware = jest.fn((req, _res, next) => {
        req.user = {
          profile: {
            id: 'google123',
            provider: 'google',
            email: '<EMAIL>',
            name: 'Test User',
          },
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
        };
        next();
      });
      mockPassportConfig.getAuthenticateMiddleware.mockReturnValue(mockPassportMiddleware);

      const response = await request(app).get('/oauth/google/callback').query({
        code: 'auth_code_123',
        state: 'state_123',
      });

      expect(response.status).toBe(302);
      expect(response.headers['location']).toBe('http://localhost:3000/dashboard');
    });

    it('should handle OAuth provider error', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthConfig.getRedirectUrls.mockReturnValue({
        success: 'http://localhost:3000/dashboard',
        failure: 'http://localhost:3000/login?error=oauth_failed',
        baseUrl: 'http://localhost:3000',
      });

      const response = await request(app).get('/oauth/google/callback').query({
        error: 'access_denied',
        error_description: 'User denied access',
      });

      expect(response.status).toBe(302);
      expect(response.headers['location']).toContain('error=access_denied');
    });

    it('should handle state parameter mismatch', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthConfig.getRedirectUrls.mockReturnValue({
        success: 'http://localhost:3000/dashboard',
        failure: 'http://localhost:3000/login?error=oauth_failed',
        baseUrl: 'http://localhost:3000',
      });

      // Create a separate app instance for this test with different session state
      const testApp = express();
      testApp.use(express.json());

      // Session with different state to create mismatch
      testApp.use((req, _res, next) => {
        req.session = {
          oauthState: 'valid_session_state', // Different from query state
          oauthStateTimestamp: Date.now(),
        } as any;
        next();
      });

      // Setup routes
      testApp.get('/oauth/:provider/callback', OAuthController.handleCallback);

      // Error handling middleware
      testApp.use((error: any, _req: any, res: any) => {
        if (error instanceof HttpException) {
          res.status(error.status).json({ message: error.message });
        } else {
          res.status(500).json({ message: 'Something went wrong' });
        }
      });

      const response = await request(testApp).get('/oauth/google/callback').query({
        code: 'auth_code_123',
        state: 'invalid_state', // This won't match the session state
      });

      expect(response.status).toBe(302);
      expect(response.headers['location']).toContain('error=state_mismatch');
    });

    it('should handle OAuth service errors', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthConfig.getRedirectUrls.mockReturnValue({
        success: 'http://localhost:3000/dashboard',
        failure: 'http://localhost:3000/login?error=oauth_failed',
        baseUrl: 'http://localhost:3000',
      });

      mockOAuthService.handleOAuthCallback.mockRejectedValue(new Error('OAuth processing failed'));

      // Mock Passport middleware
      const mockPassportMiddleware = jest.fn((req, _res, next) => {
        req.user = {
          profile: {
            id: 'google123',
            provider: 'google',
            email: '<EMAIL>',
          },
          accessToken: 'access_token',
          refreshToken: 'refresh_token',
        };
        next();
      });
      mockPassportConfig.getAuthenticateMiddleware.mockReturnValue(mockPassportMiddleware);

      const response = await request(app).get('/oauth/google/callback').query({
        code: 'auth_code_123',
        state: 'state_123', // Match the session state
      });

      expect(response.status).toBe(302);
      expect(response.headers['location']).toContain('error=processing_failed');
    });
  });

  describe('GET /oauth/accounts', () => {
    it('should return user linked OAuth accounts', async () => {
      const mockAccounts = [
        {
          id: 'account1',
          provider: 'google',
          providerAccountId: 'google123',
        },
        {
          id: 'account2',
          provider: 'facebook',
          providerAccountId: 'fb123',
        },
      ];

      mockOAuthService.getUserOAuthAccounts.mockResolvedValue(mockAccounts);
      mockOAuthConfig.getEnabledProviders.mockReturnValue(['google', 'facebook', 'github']);

      const response = await request(app).get('/oauth/accounts');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        data: {
          accounts: mockAccounts.map(account => ({
            id: account.id,
            provider: account.provider,
          })),
          enabledProviders: ['google', 'facebook', 'github'],
        },
        timestamp: expect.any(String),
      });
    });

    it('should handle errors when getting linked accounts', async () => {
      mockOAuthService.getUserOAuthAccounts.mockRejectedValue(new Error('Database error'));

      const response = await request(app).get('/oauth/accounts');

      expect(response.status).toBe(500);
    });
  });

  describe('POST /oauth/link/:provider', () => {
    it('should initiate OAuth account linking', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthConfig.getProviderConfig.mockReturnValue({
        clientID: 'test_client_id',
        clientSecret: 'test_client_secret',
        callbackURL: 'http://localhost:3000/auth/oauth/google/callback',
        scope: ['profile', 'email'],
      });

      mockOAuthService.getUserOAuthAccounts.mockResolvedValue([
        { id: 'account1', provider: 'facebook', providerAccountId: 'fb123' },
      ]);

      // Mock Passport middleware
      const mockPassportMiddleware = jest.fn((_req, res) => {
        res.redirect('https://accounts.google.com/oauth/authorize?client_id=test&state=link');
      });
      mockPassportConfig.getAuthenticateMiddleware.mockReturnValue(mockPassportMiddleware);

      const response = await request(app).post('/oauth/link/google');

      expect(response.status).toBe(302);
      expect(mockOAuthService.getUserOAuthAccounts).toHaveBeenCalledWith('user123');
    });

    it('should reject linking already linked provider', async () => {
      mockOAuthConfig.isProviderEnabled.mockReturnValue(true);
      mockOAuthService.getUserOAuthAccounts.mockResolvedValue([
        { id: 'account1', provider: 'google', providerAccountId: 'google123' },
      ]);

      const response = await request(app).post('/oauth/link/google');

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('already linked');
    });
  });

  describe('DELETE /oauth/unlink/:provider', () => {
    it('should unlink OAuth account successfully', async () => {
      mockOAuthService.unlinkOAuthAccount.mockResolvedValue(undefined);

      const response = await request(app).delete('/oauth/unlink/google');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        success: true,
        message: 'google account unlinked successfully',
        timestamp: expect.any(String),
      });
      expect(mockOAuthService.unlinkOAuthAccount).toHaveBeenCalledWith('user123', 'google');
    });

    it('should handle unlinking errors', async () => {
      mockOAuthService.unlinkOAuthAccount.mockRejectedValue(new Error('Cannot unlink the only authentication method'));

      const response = await request(app).delete('/oauth/unlink/google');

      expect(response.status).toBe(500);
    });
  });
});
