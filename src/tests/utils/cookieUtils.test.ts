import { Response, Request } from 'express';
import { CookieUtils, COOKIE_NAMES } from '@/utils/cookieUtils';

// Mock the config
jest.mock('@/config', () => ({
  NODE_ENV: 'test',
}));

// Mock JwtUtils
jest.mock('@/utils/jwt', () => ({
  JwtUtils: {
    getTokenExpiryMs: jest.fn(() => ({
      access: 15 * 60 * 1000, // 15 minutes
      refresh: 30 * 24 * 60 * 60 * 1000, // 30 days
    })),
  },
}));

describe('CookieUtils', () => {
  let mockResponse: Partial<Response>;
  let mockRequest: Partial<Request>;

  beforeEach(() => {
    mockResponse = {
      cookie: jest.fn(),
      clearCookie: jest.fn(),
    };

    mockRequest = {
      cookies: {},
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('setAccessTokenCookie', () => {
    it('should set access token cookie with correct options', () => {
      const token = 'test-access-token';

      CookieUtils.setAccessTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        token,
        expect.objectContaining({
          httpOnly: true,
          secure: false, // NODE_ENV is 'test'
          sameSite: 'strict',
          path: '/',
          maxAge: 15 * 60 * 1000, // 15 minutes
        }),
      );
    });
  });

  describe('setRefreshTokenCookie', () => {
    it('should set refresh token cookie with correct options', () => {
      const token = 'test-refresh-token';

      CookieUtils.setRefreshTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.REFRESH_TOKEN,
        token,
        expect.objectContaining({
          httpOnly: true,
          secure: false, // NODE_ENV is 'test'
          sameSite: 'strict',
          path: '/',
          maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
        }),
      );
    });
  });

  describe('setTokenCookies', () => {
    it('should set both access and refresh token cookies', () => {
      const accessToken = 'test-access-token';
      const refreshToken = 'test-refresh-token';

      CookieUtils.setTokenCookies(mockResponse as Response, accessToken, refreshToken);

      expect(mockResponse.cookie).toHaveBeenCalledTimes(2);
      expect(mockResponse.cookie).toHaveBeenCalledWith(COOKIE_NAMES.ACCESS_TOKEN, accessToken, expect.any(Object));
      expect(mockResponse.cookie).toHaveBeenCalledWith(COOKIE_NAMES.REFRESH_TOKEN, refreshToken, expect.any(Object));
    });
  });

  describe('clearAccessTokenCookie', () => {
    it('should clear access token cookie', () => {
      CookieUtils.clearAccessTokenCookie(mockResponse as Response);

      expect(mockResponse.clearCookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        expect.objectContaining({
          httpOnly: true,
          secure: false,
          sameSite: 'strict',
          path: '/',
          maxAge: 0,
        }),
      );
    });
  });

  describe('clearRefreshTokenCookie', () => {
    it('should clear refresh token cookie', () => {
      CookieUtils.clearRefreshTokenCookie(mockResponse as Response);

      expect(mockResponse.clearCookie).toHaveBeenCalledWith(
        COOKIE_NAMES.REFRESH_TOKEN,
        expect.objectContaining({
          httpOnly: true,
          secure: false,
          sameSite: 'strict',
          path: '/',
          maxAge: 0,
        }),
      );
    });
  });

  describe('clearTokenCookies', () => {
    it('should clear both access and refresh token cookies', () => {
      CookieUtils.clearTokenCookies(mockResponse as Response);

      expect(mockResponse.clearCookie).toHaveBeenCalledTimes(2);
      expect(mockResponse.clearCookie).toHaveBeenCalledWith(COOKIE_NAMES.ACCESS_TOKEN, expect.any(Object));
      expect(mockResponse.clearCookie).toHaveBeenCalledWith(COOKIE_NAMES.REFRESH_TOKEN, expect.any(Object));
    });
  });

  describe('getAccessTokenFromCookies', () => {
    it('should return access token from cookies', () => {
      mockRequest.cookies = {
        [COOKIE_NAMES.ACCESS_TOKEN]: 'test-access-token',
      };

      const token = CookieUtils.getAccessTokenFromCookies(mockRequest);

      expect(token).toBe('test-access-token');
    });

    it('should return null when access token cookie is missing', () => {
      mockRequest.cookies = {};

      const token = CookieUtils.getAccessTokenFromCookies(mockRequest);

      expect(token).toBeNull();
    });

    it('should return null when cookies object is undefined', () => {
      const requestWithUndefinedCookies = { cookies: undefined } as any;

      const token = CookieUtils.getAccessTokenFromCookies(requestWithUndefinedCookies);

      expect(token).toBeNull();
    });
  });

  describe('getRefreshTokenFromCookies', () => {
    it('should return refresh token from cookies', () => {
      mockRequest.cookies = {
        [COOKIE_NAMES.REFRESH_TOKEN]: 'test-refresh-token',
      };

      const token = CookieUtils.getRefreshTokenFromCookies(mockRequest);

      expect(token).toBe('test-refresh-token');
    });

    it('should return null when refresh token cookie is missing', () => {
      mockRequest.cookies = {};

      const token = CookieUtils.getRefreshTokenFromCookies(mockRequest);

      expect(token).toBeNull();
    });

    it('should return null when cookies object is undefined', () => {
      const requestWithUndefinedCookies = { cookies: undefined } as any;

      const token = CookieUtils.getRefreshTokenFromCookies(requestWithUndefinedCookies);

      expect(token).toBeNull();
    });
  });

  describe('validateCookieSecurity', () => {
    it('should return secure validation in test environment', () => {
      const result = CookieUtils.validateCookieSecurity();

      expect(result.isSecure).toBe(true);
      expect(result.warnings).toEqual([]);
    });

    it('should warn about insecure cookies in production', async () => {
      // Temporarily mock NODE_ENV as production
      const originalEnv = process.env['NODE_ENV'];
      process.env['NODE_ENV'] = 'production';

      // Re-require the module to pick up the new NODE_ENV
      jest.resetModules();
      const { CookieUtils: ProdCookieUtils } = await import('@/utils/cookieUtils');

      const result = ProdCookieUtils.validateCookieSecurity();

      // Restore original environment
      process.env['NODE_ENV'] = originalEnv;

      expect(result.isSecure).toBe(true); // Should still be secure in our implementation
      expect(result.warnings).toEqual([]);
    });
  });

  describe('Cookie security attributes', () => {
    it('should use secure cookies in production', async () => {
      // Mock production environment
      const mockProdConfig = { NODE_ENV: 'production' };
      jest.doMock('@/config', () => mockProdConfig);

      // Re-import to get production config
      jest.resetModules();
      const { CookieUtils: ProdCookieUtils } = await import('@/utils/cookieUtils');

      const token = 'test-token';
      ProdCookieUtils.setAccessTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        token,
        expect.objectContaining({
          secure: true, // Should be true in production
        }),
      );
    });

    it('should always use HttpOnly attribute', () => {
      const token = 'test-token';

      CookieUtils.setAccessTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        token,
        expect.objectContaining({
          httpOnly: true,
        }),
      );
    });

    it('should always use SameSite strict', () => {
      const token = 'test-token';

      CookieUtils.setAccessTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        token,
        expect.objectContaining({
          sameSite: 'strict',
        }),
      );
    });

    it('should set correct path', () => {
      const token = 'test-token';

      CookieUtils.setAccessTokenCookie(mockResponse as Response, token);

      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        token,
        expect.objectContaining({
          path: '/',
        }),
      );
    });
  });

  describe('Cookie names constants', () => {
    it('should have correct cookie names', () => {
      expect(COOKIE_NAMES.ACCESS_TOKEN).toBe('accessToken');
      expect(COOKIE_NAMES.REFRESH_TOKEN).toBe('refreshToken');
    });

    it('should have immutable cookie names', () => {
      expect(() => {
        // @ts-expect-error - Testing runtime immutability
        COOKIE_NAMES.ACCESS_TOKEN = 'modified';
      }).toThrow();
    });
  });
});
