import { Prisma } from '@prisma/client';
import { prisma } from './prisma';
import { logger } from '../utils/logger';

// Type definitions for DAL operations
interface TransactionOptions {
  maxWait?: number;
  timeout?: number;
  isolationLevel?: Prisma.TransactionIsolationLevel;
}



interface PrismaModel {
  findMany: (args?: any) => Promise<any[]>;
  count: (args?: any) => Promise<number>;
  update: (args: any) => Promise<any>;
  upsert: (args: any) => Promise<any>;
  createMany: (args: any) => Promise<any>;
  [key: string]: any;
}

/**
 * Data Access Layer (DAL) helper functions for common database operations
 */
export class DAL {
  /**
   * Execute a transaction with error handling and logging
   */
  public static async transaction<T>(
    fn: (tx: Prisma.TransactionClient) => Promise<T>,
    options?: {
      maxWait?: number;
      timeout?: number;
      isolationLevel?: Prisma.TransactionIsolationLevel;
    },
  ): Promise<T> {
    try {
      logger.debug('Starting database transaction');

      const transactionOptions: TransactionOptions = {
        maxWait: options?.maxWait || 5000, // default 5s
        timeout: options?.timeout || 10000, // default 10s
      };

      if (options?.isolationLevel) {
        transactionOptions.isolationLevel = options.isolationLevel;
      }

      const result = await prisma.$transaction(fn, transactionOptions);

      logger.debug('Database transaction completed successfully');
      return result;
    } catch (error) {
      logger.error('Database transaction failed:', error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query with error handling
   */
  public static async executeRaw(query: TemplateStringsArray | Prisma.Sql, ...values: unknown[]): Promise<number> {
    try {
      logger.debug('Executing raw SQL query');
      const result = await prisma.$executeRaw(query, ...values);
      logger.debug(`Raw SQL query affected ${result} rows`);
      return result;
    } catch (error) {
      logger.error('Raw SQL query failed:', error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query and return results
   */
  public static async queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: unknown[]): Promise<T> {
    try {
      logger.debug('Executing raw SQL query with results');
      const result = await prisma.$queryRaw<T>(query, ...values);
      logger.debug('Raw SQL query executed successfully');
      return result;
    } catch (error) {
      logger.error('Raw SQL query with results failed:', error);
      throw error;
    }
  }

  /**
   * Batch operations with error handling
   */
  public static async batch(queries: Prisma.PrismaPromise<any>[]): Promise<any[]> {
    try {
      logger.debug(`Executing batch of ${queries.length} operations`);
      const results = await prisma.$transaction(queries);
      logger.debug('Batch operations completed successfully');
      return results;
    } catch (error) {
      logger.error('Batch operations failed:', error);
      throw error;
    }
  }

  /**
   * Generic paginated query helper
   */
  public static async paginate<T>(
    model: PrismaModel,
    args: any,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;
      const take = limit;

      const [data, total] = await Promise.all([
        model.findMany({
          ...args,
          skip,
          take,
        }),
        model.count({
          where: (args as any).where,
        }),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: data as T[],
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Pagination query failed:', error);
      throw error;
    }
  }

  /**
   * Soft delete helper (if using soft deletes)
   */
  public static async softDelete(model: PrismaModel, where: Record<string, unknown>, deletedAtField: string = 'deletedAt'): Promise<unknown> {
    try {
      logger.debug('Performing soft delete operation');
      const result = await model.update({
        where,
        data: {
          [deletedAtField]: new Date(),
        },
      });
      logger.debug('Soft delete completed successfully');
      return result;
    } catch (error) {
      logger.error('Soft delete failed:', error);
      throw error;
    }
  }

  /**
   * Upsert operation with error handling
   */
  public static async upsert<T>(model: PrismaModel, where: Record<string, unknown>, create: Record<string, unknown>, update: Record<string, unknown>): Promise<T> {
    try {
      logger.debug('Performing upsert operation');
      const result = await model.upsert({
        where,
        create,
        update,
      });
      logger.debug('Upsert operation completed successfully');
      return result as T;
    } catch (error) {
      logger.error('Upsert operation failed:', error);
      throw error;
    }
  }

  /**
   * Bulk create operations
   */
  public static async createMany<T>(
    model: PrismaModel,
    data: T[],
    skipDuplicates: boolean = false,
  ): Promise<{ count: number }> {
    try {
      logger.debug(`Performing bulk create of ${data.length} records`);
      const result = await model.createMany({
        data,
        skipDuplicates,
      });
      logger.debug(`Bulk create completed successfully: ${result.count} records created`);
      return result;
    } catch (error) {
      logger.error('Bulk create failed:', error);
      throw error;
    }
  }

  /**
   * Health check for database connection
   */
  public static async healthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      logger.error('Database health check failed:', error);
      return false;
    }
  }
}

// Export common database operations
export const { transaction, executeRaw, queryRaw, batch, paginate, softDelete, upsert, createMany, healthCheck } = DAL;
