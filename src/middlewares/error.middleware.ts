import { NextFunction, Request, Response } from 'express';
import { HttpException } from '../exceptions/HttpException';
import { ValidationException } from './validation.middleware';
import { logger } from '../utils/secureLogger';
import { sanitizeError } from '../utils/logSanitizer';
import { v4 as uuidv4 } from 'uuid';

// Standardized error response interface
interface ErrorResponse {
  success: false;
  message: string;
  error?: {
    code?: string;
    details?: string;
  };
  timestamp: string;
  path: string;
  correlationId: string;
}

const errorMiddleware = (error: HttpException | ValidationException | Error, req: Request, res: Response, _next: NextFunction) => {
  try {
    // Generate correlation ID for error tracking
    const correlationId = uuidv4();

    // Determine status code and message
    const status: number = error instanceof HttpException ? error.status : 500;
    let message: string = error.message || 'Internal server error';

    // Create standardized error response
    const errorResponse: ErrorResponse = {
      success: false,
      message,
      timestamp: new Date().toISOString(),
      path: req.originalUrl || req.path,
      correlationId,
    };

    // Handle validation errors specially
    if (error instanceof ValidationException) {
      message = 'Validation failed';
      errorResponse.message = message;
      errorResponse.error = {
        code: 'VALIDATION_ERROR',
        details: JSON.stringify(error.getFormattedErrors()),
      };
    } else if (process.env['NODE_ENV'] !== 'production' || status < 500) {
      // Add error details for non-production environments or specific error types
      errorResponse.error = {
        code: error instanceof HttpException ? error.name : error.constructor.name,
        details: status >= 500 ? 'Internal server error' : error.message,
      };
    }

    // Log error with correlation ID and sanitized details
    logger.error(`[${req.method}] ${req.path} >> StatusCode: ${status}, Message: ${message}`, {
      correlationId,
      status,
      method: req.method,
      path: req.path,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user ? (req.user as { id: string }).id : undefined,
      error: sanitizeError(error),
    });

    res.status(status).json(errorResponse);
  } catch (internalError) {
    // Fallback error handling
    logger.error('Error middleware failed', {
      originalError: sanitizeError(error),
      internalError: sanitizeError(internalError),
      path: req.path,
      method: req.method,
    });

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      timestamp: new Date().toISOString(),
      path: req.originalUrl || req.path,
      correlationId: uuidv4(),
    });
  }
};

export default errorMiddleware;
