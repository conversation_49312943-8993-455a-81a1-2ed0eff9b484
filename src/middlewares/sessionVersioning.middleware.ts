import { Request, Response, NextFunction } from 'express';
import { setTimeout } from 'timers';
import { SessionVersioningService } from '../services/sessionVersioning.service';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { User as UserInterface } from '../interfaces/user.interface';

// Concurrency control for session validation
const sessionValidationLocks = new Map<string, Promise<boolean>>();

// Cleanup old locks periodically to prevent memory leaks (only in production)
let cleanupInterval: ReturnType<typeof setTimeout> | null = null;
if (process.env['NODE_ENV'] === 'production') {
  cleanupInterval = setInterval(() => {
    if (sessionValidationLocks.size > 1000) {
      logger.warn('High number of session validation locks, clearing all', {
        lockCount: sessionValidationLocks.size,
      });
      sessionValidationLocks.clear();
    }
  }, 60000); // Check every minute
}

// Export cleanup function for testing
export const cleanupSessionLocks = () => {
  sessionValidationLocks.clear();
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
};

/**
 * Perform session validation with proper error handling
 */
async function performSessionValidation(req: Request, res: Response): Promise<boolean> {
  try {
    // Update session activity
    SessionVersioningService.updateSessionActivity(req);

    // Validate session version
    const isValid = await SessionVersioningService.validateSessionVersion(req);

    if (!isValid) {
      // Session version is invalid - invalidate and redirect to login
      try {
        await SessionVersioningService.invalidateSession(req, res);
      } catch (invalidationError) {
        logger.error('Session invalidation failed', {
          userId: req.session?.userId,
          error: invalidationError instanceof Error ? invalidationError.message : 'Unknown error',
        });
        // Continue with the false response even if invalidation fails
      }

      logger.warn('Invalid session version detected, session invalidated', {
        userId: req.session?.userId,
        sessionId: req.sessionID?.substring(0, 8) + '...',
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      return false;
    }

    return true;
  } catch (error) {
    logger.error('Session validation error', {
      userId: req.session?.userId,
      sessionId: req.sessionID?.substring(0, 8) + '...',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return false;
  }
}

/**
 * Session versioning middleware with concurrency control
 * Validates session versions and handles session invalidation
 */
export const validateSessionVersion = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Skip validation for public routes
    if (!req.session || !req.session.userId) {
      return next();
    }

    const sessionKey = `${req.session.userId}:${req.sessionID}`;

    // Check if validation is already in progress for this session
    if (sessionValidationLocks.has(sessionKey)) {
      try {
        const isValid = await sessionValidationLocks.get(sessionKey)!;
        if (!isValid) {
          return next(new HttpException(401, 'Session expired due to security changes. Please log in again.'));
        }
        return next();
      } catch (error) {
        logger.error('Session validation lock failed', {
          userId: req.session.userId,
          sessionId: req.sessionID?.substring(0, 8) + '...',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
        return next(new HttpException(500, 'Session validation error'));
      }
    }

    // Create validation promise and store it
    const validationPromise = performSessionValidation(req, res);
    sessionValidationLocks.set(sessionKey, validationPromise);

    try {
      const isValid = await validationPromise;

      if (!isValid) {
        return next(new HttpException(401, 'Session expired due to security changes. Please log in again.'));
      }

      next();
    } finally {
      // Clean up the lock
      sessionValidationLocks.delete(sessionKey);
    }
  } catch (error) {
    logger.error('Session version validation failed', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Fail secure - invalidate session on error
    if (req.session && req.session.userId) {
      try {
        await SessionVersioningService.invalidateSession(req, res);
      } catch (invalidationError) {
        logger.error('Session invalidation failed during error handling', {
          userId: req.session.userId,
          error: invalidationError instanceof Error ? invalidationError.message : 'Unknown error',
        });
        // Continue with the 401 response even if invalidation fails
      }
    }

    next(new HttpException(401, 'Session validation failed. Please log in again.'));
  }
};

/**
 * Initialize session versioning for authenticated users
 */
export const initializeSessionVersioning = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // This middleware should be called after successful authentication
    // The user object should be available in res.locals or req.user
    const user = res.locals['user'] || req['user'];

    if (user && req.session) {
      await SessionVersioningService.initializeSession(req, user);
    }

    next();
  } catch (error) {
    logger.error('Failed to initialize session versioning', {
      userId: (res.locals['user'] as UserInterface)?.id || (req['user'] as UserInterface)?.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Don't fail the request, just log the error
    next();
  }
};

/**
 * Middleware to handle role changes
 */
export const handleRoleChange = (oldRole: string, newRole: string) => {
  return async (req: Request, _res: Response, next: NextFunction) => {
    try {
      if (req.session && req.session.userId) {
        await SessionVersioningService.handleRoleChange(req.session.userId, oldRole, newRole);

        // Update session role
        req.session.userRole = newRole;
        req.session.lastRoleChange = new Date().toISOString();
      }

      next();
    } catch (error) {
      logger.error('Failed to handle role change in middleware', {
        userId: req.session?.userId,
        oldRole,
        newRole,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      // Don't fail the request, but log the error
      next();
    }
  };
};

/**
 * Middleware to handle password changes
 */
export const handlePasswordChange = async (req: Request, _res: Response, next: NextFunction) => {
  try {
    if (req.session && req.session.userId) {
      await SessionVersioningService.handlePasswordChange(req.session.userId);
    }

    next();
  } catch (error) {
    logger.error('Failed to handle password change in middleware', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    // Don't fail the request, but log the error
    next();
  }
};

/**
 * Middleware to force session invalidation
 */
export const forceSessionInvalidation = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (req.session && req.session.userId) {
      await SessionVersioningService.invalidateSession(req, res);
    }

    next();
  } catch (error) {
    logger.error('Failed to force session invalidation', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    next();
  }
};

/**
 * Get session statistics endpoint middleware
 */
export const getSessionStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.session || !req.session.userId) {
      return next(new HttpException(401, 'No active session'));
    }

    const stats = await SessionVersioningService.getSessionStats(req.session.userId);

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error('Failed to get session statistics', {
      userId: req.session?.userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    next(new HttpException(500, 'Failed to retrieve session statistics'));
  }
};

export { SessionVersioningService };
