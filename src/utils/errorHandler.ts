import { HttpException } from '../exceptions/HttpException';

/**
 * Centralized error creation utilities for consistent error handling
 */
export class <PERSON>rrorHandler {
  /**
   * Create a standardized HTTP exception
   */
  static createHttpError(status: number, message: string, code?: string): HttpException {
    const error = new HttpException(status, message);
    if (code) {
      error.name = code;
    }
    return error;
  }

  /**
   * Create a bad request error (400)
   */
  static badRequest(message: string = 'Bad request', code?: string): HttpException {
    return this.createHttpError(400, message, code);
  }

  /**
   * Create an unauthorized error (401)
   */
  static unauthorized(message: string = 'Unauthorized', code?: string): HttpException {
    return this.createHttpError(401, message, code);
  }

  /**
   * Create a forbidden error (403)
   */
  static forbidden(message: string = 'Forbidden', code?: string): HttpException {
    return this.createHttpError(403, message, code);
  }

  /**
   * Create a not found error (404)
   */
  static notFound(message: string = 'Resource not found', code?: string): HttpException {
    return this.createHttpError(404, message, code);
  }

  /**
   * Create a conflict error (409)
   */
  static conflict(message: string = 'Conflict', code?: string): HttpException {
    return this.createHttpError(409, message, code);
  }

  /**
   * Create a too many requests error (429)
   */
  static tooManyRequests(message: string = 'Too many requests', code?: string): HttpException {
    return this.createHttpError(429, message, code);
  }

  /**
   * Create an internal server error (500)
   */
  static internalError(message: string = 'Internal server error', code?: string): HttpException {
    return this.createHttpError(500, message, code);
  }

  /**
   * Create a service unavailable error (503)
   */
  static serviceUnavailable(message: string = 'Service unavailable', code?: string): HttpException {
    return this.createHttpError(503, message, code);
  }

  /**
   * Wrap async functions to automatically catch and forward errors
   */
  static asyncHandler<T extends unknown[], R>(
    fn: (...args: T) => Promise<R>
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        throw error instanceof HttpException ? error : this.internalError('Unexpected error occurred');
      }
    };
  }

  /**
   * Check if error is an operational error (expected) vs programming error
   */
  static isOperationalError(error: Error): boolean {
    return error instanceof HttpException;
  }

  /**
   * Extract safe error message for client response
   */
  static getSafeErrorMessage(error: Error, defaultMessage: string = 'An error occurred'): string {
    if (this.isOperationalError(error)) {
      return error.message;
    }
    
    // Don't expose internal error details in production
    if (process.env['NODE_ENV'] === 'production') {
      return defaultMessage;
    }
    
    return error.message || defaultMessage;
  }
}

/**
 * Common error codes for consistent error identification
 */
export const ErrorCodes = {
  // Authentication & Authorization
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Resources
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // Rate Limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  
  // System
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  CONFIGURATION_ERROR: 'CONFIGURATION_ERROR',
} as const;

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes];
