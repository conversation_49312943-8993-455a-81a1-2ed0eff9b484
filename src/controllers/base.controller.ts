import { NextFunction, Request, Response } from 'express';

// Standardized response interfaces
interface SuccessResponse<T = unknown> {
  success: true;
  message?: string;
  data: T;
  timestamp: string;
}

interface ErrorResponse {
  success: false;
  message: string;
  error?: {
    code?: string;
    details?: string;
  };
  timestamp: string;
}

export abstract class BaseController {
  protected catchAsync(fn: (req: Request, res: Response, next?: NextFunction) => Promise<void>) {
    return (req: Request, res: Response, next: NextFunction) => {
      fn(req, res, next).catch(next);
    };
  }

  protected sendResponse<T>(res: Response, statusCode: number, data: T, message?: string): void {
    const response: SuccessResponse<T> = {
      success: true,
      data,
      timestamp: new Date().toISOString(),
    };

    if (message) {
      response.message = message;
    }

    res.status(statusCode).json(response);
  }

  protected sendError(res: Response, statusCode: number, message: string, errorCode?: string, details?: string): void {
    const response: ErrorResponse = {
      success: false,
      message,
      timestamp: new Date().toISOString(),
    };

    if (errorCode || details) {
      response.error = {
        code: errorCode,
        details,
      };
    }

    res.status(statusCode).json(response);
  }

  /**
   * Send paginated response with metadata
   */
  protected sendPaginatedResponse<T>(
    res: Response,
    data: T[],
    total: number,
    page: number,
    limit: number,
    message?: string,
  ): void {
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    const response = {
      success: true,
      data,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage,
        hasPrevPage,
      },
      timestamp: new Date().toISOString(),
    };

    if (message) {
      (response as any).message = message;
    }

    res.status(200).json(response);
  }
}
