import { Request, Response, NextFunction } from 'express';
import { User } from '../interfaces/user.interface';
import { OAuthService } from '../services/oauth.service';
import { OAuthProvider, OAuthConfig } from '../config/oauth.config';
import { PassportConfig } from '../config/passport.config';
import { CookieUtils } from '../utils/cookieUtils';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeError } from '../utils/logSanitizer';
import { AuthContext } from '../services/auth.service';
import { SessionVersioningService } from '../services/sessionVersioning.service';

/**
 * OAuth Controller
 * Handles OAuth authentication flows with security best practices
 * Integrates with existing authentication infrastructure
 */
export class OAuthController {
  /**
   * Initiate OAuth authentication
   * GET /auth/oauth/:provider
   */
  static async initiateAuth(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const provider = req.params['provider'] as OAuthProvider;

      // Validate provider
      if (!OAuthConfig.isProviderEnabled(provider)) {
        throw new HttpException(400, `OAuth provider '${provider}' is not enabled or configured`);
      }

      // Generate state parameter for CSRF protection
      const state = (req as any).oauthState || Math.random().toString(36).substring(2, 15);

      // Store state in session for validation
      if (req.session) {
        req.session.oauthState = state;
      }

      // Get authentication URL with state
      const authOptions = {
        state,
        scope: OAuthConfig.getProviderConfig(provider).scope,
      };

      logger.info('OAuth authentication initiated', {
        provider,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        state: state.substring(0, 8) + '...',
      });

      // Use Passport to redirect to OAuth provider
      PassportConfig.getAuthenticateMiddleware(provider, authOptions)(req, res, next);
    } catch (error) {
      logger.error('OAuth initiation failed', {
        provider: req.params['provider'],
        error: sanitizeError(error),
        ip: req.ip,
      });
      next(error);
    }
  }

  /**
   * Handle OAuth callback
   * GET /auth/oauth/:provider/callback
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  static async handleCallback(req: Request, res: Response, _next: NextFunction): Promise<void> {
    try {
      const provider = req.params['provider'] as OAuthProvider;

      // Validate provider
      if (!OAuthConfig.isProviderEnabled(provider)) {
        return res.redirect(OAuthConfig.getRedirectUrls().failure + '?error=invalid_provider');
      }

      // Check for OAuth errors
      if (req.query['error']) {
        logger.warn('OAuth provider returned error', {
          provider,
          error: req.query['error'],
          errorDescription: req.query['error_description'],
          ip: req.ip,
        });
        const failureUrl = OAuthConfig.getRedirectUrls().failure;
        const separator = failureUrl.includes('?') ? '&' : '?';
        return res.redirect(failureUrl + `${separator}error=${req.query['error']}`);
      }

      // Validate state parameter (CSRF protection)
      const receivedState = req.query['state'] as string;
      const sessionState = req.session?.oauthState;

      if (!receivedState || !sessionState || receivedState !== sessionState) {
        logger.warn('OAuth state validation failed', {
          provider,
          hasReceivedState: !!receivedState,
          hasSessionState: !!sessionState,
          statesMatch: receivedState === sessionState,
          ip: req.ip,
        });
        const failureUrl = OAuthConfig.getRedirectUrls().failure;
        const separator = failureUrl.includes('?') ? '&' : '?';
        return res.redirect(failureUrl + `${separator}error=state_mismatch`);
      }

      // Clear state from session
      delete req.session.oauthState;

      // Use Passport to authenticate with provider
      const failureUrl = OAuthConfig.getRedirectUrls().failure;
      const separator = failureUrl.includes('?') ? '&' : '?';

      PassportConfig.getAuthenticateMiddleware(provider, {
        failureRedirect: failureUrl + `${separator}error=auth_failed`,
      })(req, res, async (err: any) => {
        if (err) {
          logger.error('Passport authentication failed', {
            provider,
            error: sanitizeError(err),
            ip: req.ip,
          });
          return res.redirect(failureUrl + `${separator}error=auth_failed`);
        }

        try {
          // Extract OAuth data from Passport
          const oauthData = req.user as any;
          if (!oauthData || !oauthData.profile) {
            throw new HttpException(400, 'Invalid OAuth response from provider');
          }

          // Build auth context
          const authContext: AuthContext = {
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            deviceFingerprint: (req as any).deviceFingerprint,
          };

          // Handle OAuth authentication
          const result = await OAuthService.handleOAuthCallback(
            oauthData.profile,
            oauthData.accessToken,
            oauthData.refreshToken,
            authContext,
          );

          // Set secure HTTP-only cookies for JWT tokens
          CookieUtils.setTokenCookies(res, result.token, result.refreshToken);

          // Initialize session versioning
          const user = await OAuthService.findUserById(result.user.id!);
          if (user) {
            await SessionVersioningService.initializeSession(req, user);
          }

          logger.info('OAuth authentication successful', {
            provider,
            userId: result.user.id,
            isNewUser: result.isNewUser,
            linkedAccount: result.linkedAccount,
            ip: req.ip,
          });

          // Redirect to success URL
          const redirectUrl = result.isNewUser
            ? OAuthConfig.getRedirectUrls().success + '?welcome=true'
            : OAuthConfig.getRedirectUrls().success;

          res.redirect(redirectUrl);
        } catch (error) {
          logger.error('OAuth callback processing failed', {
            provider,
            error: sanitizeError(error),
            ip: req.ip,
          });
          const failureUrl = OAuthConfig.getRedirectUrls().failure;
          const separator = failureUrl.includes('?') ? '&' : '?';
          res.redirect(failureUrl + `${separator}error=processing_failed`);
        }
      });
    } catch (error) {
      logger.error('OAuth callback handler failed', {
        provider: req.params['provider'],
        error: sanitizeError(error),
        ip: req.ip,
      });
      const failureUrl = OAuthConfig.getRedirectUrls().failure;
      const separator = failureUrl.includes('?') ? '&' : '?';
      res.redirect(failureUrl + `${separator}error=callback_failed`);
    }
  }

  /**
   * Link OAuth account to existing user
   * POST /auth/oauth/link/:provider
   */
  static async linkAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const provider = req.params['provider'] as OAuthProvider;
      const userId = (req.user as User)!.id;

      // Validate provider
      if (!OAuthConfig.isProviderEnabled(provider)) {
        throw new HttpException(400, `OAuth provider '${provider}' is not enabled or configured`);
      }

      // Check if account is already linked
      const existingAccounts = await OAuthService.getUserOAuthAccounts(userId);
      const alreadyLinked = existingAccounts.some((account: any) => account.provider === provider);

      if (alreadyLinked) {
        throw new HttpException(400, `${provider} account is already linked to your account`);
      }

      // Generate state for linking
      const state = Math.random().toString(36).substring(2, 15) + '_link_' + userId;

      if (req.session) {
        req.session.oauthLinkState = state;
        req.session.oauthLinkUserId = userId;
      }

      // Get authentication URL for linking
      const authOptions = {
        state,
        scope: OAuthConfig.getProviderConfig(provider).scope,
      };

      logger.info('OAuth account linking initiated', {
        provider,
        userId,
        ip: req.ip,
      });

      // Use Passport to redirect to OAuth provider
      PassportConfig.getAuthenticateMiddleware(provider, authOptions)(req, res, next);
    } catch (error) {
      logger.error('OAuth account linking initiation failed', {
        provider: req.params['provider'],
        userId: (req.user as User)?.id,
        error: sanitizeError(error),
      });
      next(error);
    }
  }

  /**
   * Unlink OAuth account
   * DELETE /auth/oauth/unlink/:provider
   */
  static async unlinkAccount(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const provider = req.params['provider'] as OAuthProvider;
      const userId = (req.user as User)!.id;

      await OAuthService.unlinkOAuthAccount(userId, provider);

      logger.info('OAuth account unlinked successfully', {
        provider,
        userId,
        ip: req.ip,
      });

      res.status(200).json({
        success: true,
        message: `${provider} account unlinked successfully`,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('OAuth account unlinking failed', {
        provider: req.params['provider'],
        userId: (req.user as User)?.id,
        error: sanitizeError(error),
      });
      next(error);
    }
  }

  /**
   * Get user's linked OAuth accounts
   * GET /auth/oauth/accounts
   */
  static async getLinkedAccounts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = (req.user as User)!.id;
      const accounts = await OAuthService.getUserOAuthAccounts(userId);

      res.status(200).json({
        success: true,
        data: {
          accounts: accounts.map((account: any) => ({
            id: account.id,
            provider: account.provider,
          })),
          enabledProviders: OAuthConfig.getEnabledProviders(),
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to get linked OAuth accounts', {
        userId: (req.user as User)?.id,
        error: sanitizeError(error),
      });
      next(error);
    }
  }

  /**
   * Get available OAuth providers
   * GET /auth/oauth/providers
   */
  static async getAvailableProviders(_req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const enabledProviders = OAuthConfig.getEnabledProviders();
      const providerConfigs = enabledProviders.map(provider => ({
        name: provider,
        displayName: provider.charAt(0).toUpperCase() + provider.slice(1),
        authUrl: `/api/v1/auth/oauth/${provider}`,
        enabled: true,
      }));

      res.status(200).json({
        success: true,
        data: {
          providers: providerConfigs,
          total: enabledProviders.length,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Failed to get available OAuth providers', {
        error: sanitizeError(error),
      });
      next(error);
    }
  }
}
