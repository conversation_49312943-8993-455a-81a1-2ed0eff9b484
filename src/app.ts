import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import hpp from 'hpp';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import passport from 'passport';
import swaggerUi from 'swagger-ui-express';

import { ORIGIN, CREDENTIALS } from './config';
import { logger } from './utils/secureLogger';
import { errorMiddleware, loggerMiddleware, responseLoggerMiddleware, rateLimiterMiddleware } from './middlewares';
import { generalApiRateLimit } from './middlewares/security.middleware';
import { generateCSRFToken, verifyCSRFToken, sessionConfig } from './middlewares/csrf.middleware';
import { validateSessionVersion } from './middlewares/sessionVersioning.middleware';
import routes from './routes';
import { RBACService } from './services/rbac.service';
import { KeyRotationService } from './services/keyRotation.service';
import { swaggerSpec } from './config/swagger';
import { PassportConfig } from './config/passport.config';

// Create Express application instance
const app = express();

// Initialize RBAC system with default permissions
RBACService.initialize();
logger.info('RBAC system initialized with default permissions');

// Initialize JWT key rotation service
KeyRotationService.initialize().catch(error => {
  logger.error('Failed to initialize JWT key rotation service', { error });
});
logger.info('JWT key rotation service initialization started');

// Initialize Passport OAuth strategies
try {
  PassportConfig.initialize();
  logger.info('Passport OAuth strategies initialized successfully');
} catch (error) {
  logger.error('Failed to initialize Passport OAuth strategies', { error });
}

// Apply security middlewares first
app.use(helmet());

// CORS configuration
app.use(
  cors({
    origin: ORIGIN,
    credentials: CREDENTIALS,
    optionsSuccessStatus: 200, // some legacy browsers (IE11, various SmartTVs) choke on 204
  }),
);

// Rate limiting
app.use(rateLimiterMiddleware);

// HPP (HTTP Parameter Pollution) protection
app.use(hpp());

// Compression middleware
app.use(compression());

// Body parsing middlewares
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Cookie parser
app.use(cookieParser());

// Session middleware for CSRF protection
app.use(session(sessionConfig));

// Initialize Passport middleware
app.use(passport.initialize());

// CSRF Protection middleware
app.use(generateCSRFToken);
app.use(verifyCSRFToken);

// Session versioning middleware (after CSRF, before routes)
app.use(validateSessionVersion);

// General API rate limiting (distributed Redis-based)
app.use(generalApiRateLimit);

// Request logging middleware
app.use(loggerMiddleware);

// Response logging middleware (secure - no token leakage)
app.use(responseLoggerMiddleware);

// Health check route
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Secure Backend Server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    features: {
      authentication: 'JWT-based with OAuth 2.0',
      authorization: 'RBAC with granular permissions',
      validation: 'class-validator with DTOs',
      rateLimit: 'express-rate-limit',
      logging: 'Winston with morgan',
      swagger: 'Available at /api-docs',
      oauth: 'Google, Facebook, GitHub',
    },
  });
});

// Swagger UI Documentation
app.use(
  '/api-docs',
  swaggerUi.serve,
  swaggerUi.setup(swaggerSpec, {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Secure Backend API Documentation',
    swaggerOptions: {
      persistAuthorization: true,
    },
  }),
);

// API Routes
app.use('/api/v1', routes);

// 404 handler for undefined routes
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl,
  });
});

// Error handling middleware (must be last)
app.use(errorMiddleware);

export default app;
